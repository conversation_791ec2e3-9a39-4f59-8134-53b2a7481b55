# iOS 动态库注入和 RPATH 修复指南

## 概述

本工具提供了完整的 iOS 应用包（IPA）动态库注入和 RPATH 配置修复解决方案。当 iOS 应用中的动态库未被正确加载时，该工具能够自动检测、修复并验证动态库的加载状态。

## 主要功能

### 1. 动态库加载状态检测
- 分析主可执行文件的动态库依赖
- 检查 LC_RPATH 配置
- 识别未正确加载的动态库
- 支持 Unity 项目的特殊处理

### 2. RPATH 配置修复
- 自动添加缺失的 RPATH 路径
- 修复动态库的 install_name
- 确保动态库能够被正确定位

### 3. 动态库注入
- 使用多种方法将未加载的动态库注入到主进程
- 支持 optool、insert_dylib 等专业工具
- 提供包装器库的备选方案

## 使用方法

### 基本验证
```bash
# 仅验证 RPATH 配置，不进行修复
onetools ipa rpath -p /path/to/app.ipa
```

### 验证并修复
```bash
# 验证并自动修复检测到的问题
onetools ipa rpath -p /path/to/app.ipa --fix
```

## 工作流程

### 1. 分析阶段
1. 解压 IPA 文件到临时目录
2. 定位主可执行文件（支持 Unity 项目的 UnityFramework）
3. 使用 `otool` 分析动态库依赖和 RPATH 配置
4. 扫描 Frameworks 目录中的自定义动态库
5. 识别未正确加载的动态库

### 2. 修复阶段（使用 --fix 参数时）
1. **RPATH 修复**：使用 `install_name_tool` 添加缺失的 RPATH
2. **动态库 install_name 修复**：确保库的 install_name 与期望的引用路径匹配
3. **动态库注入**：将未加载的库添加到主进程的 Load Commands 中

### 3. 验证阶段
1. 重新分析修复后的配置
2. 验证所有问题是否已解决
3. 重新打包 IPA 文件

## 动态库注入方法

### 方法 1: optool（推荐）
```bash
# 安装 optool
brew install optool

# 工具会自动使用 optool 进行注入
optool install -c load -p @rpath/YourLibrary.framework/YourLibrary -t MainExecutable
```

### 方法 2: insert_dylib
```bash
# 安装 insert_dylib
git clone https://github.com/Tyilo/insert_dylib.git
cd insert_dylib && make

# 工具会自动使用 insert_dylib 进行注入
insert_dylib @rpath/YourLibrary.framework/YourLibrary MainExecutable MainExecutable
```

### 方法 3: 包装器库（备选方案）
当专业工具不可用时，工具会创建一个包装器库来强制加载目标库：

```c
// 自动生成的包装器代码示例
#include <dlfcn.h>
#include <stdio.h>

__attribute__((constructor))
void load_YourLibrary_library() {
    void* handle = dlopen("@rpath/YourLibrary.framework/YourLibrary", RTLD_LAZY);
    if (!handle) {
        printf("Warning: Failed to load YourLibrary: %s\n", dlerror());
    }
}
```

## 输出示例

### 验证模式输出
```
============================================================
RPATH 配置分析结果
============================================================
应用目录: YourApp.app
主可执行文件: YourApp

1. 主要加载的动态库:
   1. /System/Library/Frameworks/Foundation.framework/Foundation
   2. /System/Library/Frameworks/UIKit.framework/UIKit
   3. @rpath/LoadedLibrary.framework/LoadedLibrary

2. LC_RPATH 路径:
   1. @executable_path/Frameworks
   2. @loader_path/Frameworks

3. 自定义动态库:
   1. Frameworks/CustomLibrary.framework/CustomLibrary
   2. Frameworks/UnloadedLibrary.dylib

4. 缺失的RPATH配置:
✓ 所有自定义动态库的rpath配置正确

5. 未正确加载的动态库:
✗ 发现 1 个未被主进程加载的动态库:
   1. UnloadedLibrary (dylib) -> @rpath/UnloadedLibrary.dylib
```

### 修复模式输出
```
[INFO] 开始修复rpath配置和动态库注入...
[INFO] 开始完整的动态库注入流程，需要处理 1 个未加载的动态库
[INFO] 处理未加载的dylib: UnloadedLibrary
[INFO] 当前库install_name: UnloadedLibrary.dylib
[INFO] 期望的引用路径: @rpath/UnloadedLibrary.dylib
[INFO] 修改库的install_name从 UnloadedLibrary.dylib 到 @rpath/UnloadedLibrary.dylib
[SUCCESS] 成功修改库install_name: UnloadedLibrary
[INFO] 将库 UnloadedLibrary 注入到主进程Load Commands中...
[INFO] 使用optool注入库...
[SUCCESS] 使用optool成功注入库: UnloadedLibrary
[SUCCESS] 成功完成动态库注入: UnloadedLibrary
[SUCCESS] 修复后的IPA文件已保存: /path/to/app_rpath_fixed.ipa
```

## 错误处理

### 常见问题及解决方案

1. **工具不可用**
   ```
   [WARNING] optool工具不可用
   [WARNING] insert_dylib工具不可用
   ```
   解决方案：安装相应的工具或使用包装器库方案

2. **库文件不存在**
   ```
   [WARNING] 库文件不存在，跳过: /path/to/library
   ```
   解决方案：确认库文件确实存在于 Frameworks 目录中

3. **权限问题**
   ```
   [ERROR] 修改库install_name失败: permission denied
   ```
   解决方案：确保对 IPA 文件和临时目录有写权限

## 技术细节

### Mach-O 文件格式处理
- 使用 `otool` 分析 Load Commands
- 使用 `install_name_tool` 修改 install_name 和添加 RPATH
- 支持 arm64 和 x86_64 架构

### Unity 项目支持
- 自动检测 UnityFramework.framework
- 将 UnityFramework 作为主可执行文件进行分析
- 处理 Unity 特有的动态库加载模式

### 安全性考虑
- 创建文件备份以防修改失败
- 验证修复结果确保正确性
- 提供详细的日志输出便于调试

## 限制和注意事项

1. **代码签名**：修复后的 IPA 需要重新签名才能在设备上运行
2. **系统库**：不会处理系统提供的动态库（如 Swift 标准库）
3. **依赖关系**：无法解决库之间的复杂依赖关系
4. **运行时加载**：某些库可能需要在代码中显式引用才能正确加载

## 最佳实践

1. 在修复前备份原始 IPA 文件
2. 在测试环境中验证修复结果
3. 结合代码审查确保库的正确使用
4. 考虑使用 dlopen() 进行动态加载作为备选方案
