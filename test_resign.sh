#!/bin/bash

# 测试resign命令的脚本
echo "开始测试IPA重签名功能（包含rpath检查）..."

# 设置测试参数
TEST_BASE_PATH="/Users/<USER>/Desktop/辅助工具/OnetoolsIPAResign"
# IPA_PATH="${TEST_BASE_PATH}/IPA/Distro_QRSL-IOS-Shipping_20250722_044002.ipa"
IPA_PATH="${TEST_BASE_PATH}/IPA/CloudHotta_v5.2.0.ipa"
#IPA_PATH="${TEST_BASE_PATH}/IPA/TL_DisHKAppStore_16_1_0_20250711174142.ipa"
BUNDLE_ID="com.laohunew.demosdk"
PROVISION_PATH="${TEST_BASE_PATH}/comlaohunewdemosdk.mobileprovision"
CERT_NAME="Apple Development: yanshan liu (25UN2ZP5WW)"
DISPLAY_NAME="老虎重签"
APP_VERSION="2.0.0"
BUILD_VERSION="200"
OUTPUT_PATH="${TEST_BASE_PATH}/output.ipa"
# APP_ICON="${TEST_BASE_PATH}/icon1024.png"
APP_ICON="${TEST_BASE_PATH}/Assets.xcassets"
ADDITIONAL_PLIST_PATH="" #"/Users/<USER>/Downloads/addTestInfo.plist"
REMOVE_PLIST_KEYS="" #"LSApplicationQueriesSchemes"
DELETE_FILES_PATH="" #PrivacyInfo.xcprivacy,zh-Hans.lproj/Main.strings"
ADD_FILES_PATH="${TEST_BASE_PATH}/test_add_files_demo"
# 可选的自定义entitlements.plist文件路径
# ENTITLEMENTS_PATH="/path/to/entitlements.plist"

# 检查输入文件是否存在
if [ ! -f "$IPA_PATH" ]; then
    echo "错误: IPA文件不存在: $IPA_PATH"
    exit 1
fi

if [ ! -f "$PROVISION_PATH" ]; then
    echo "错误: mobileprovision文件不存在: $PROVISION_PATH"
    exit 1
fi

# 删除之前的输出文件
if [ -f "$OUTPUT_PATH" ]; then
    rm "$OUTPUT_PATH"
    echo "删除之前的输出文件: $OUTPUT_PATH"
fi

# 执行重签名命令
echo "执行重签名命令..."

# 如果有自定义entitlements.plist文件，则使用它
if [ -n "$ENTITLEMENTS_PATH" ] && [ -f "$ENTITLEMENTS_PATH" ]; then
    echo "使用自定义entitlements文件: $ENTITLEMENTS_PATH"
    ./onetools ipa resign \
        -p "$IPA_PATH" \
        --bundle-id "$BUNDLE_ID" \
        --provision "$PROVISION_PATH" \
        --cert "$CERT_NAME" \
        --display-name "$DISPLAY_NAME" \
        --app-version "$APP_VERSION" \
        --build-version "$BUILD_VERSION" \
        --app-icon "$APP_ICON" \
        --additional-plist "$ADDITIONAL_PLIST_PATH" \
        --remove-plist-keys "$REMOVE_PLIST_KEYS" \
        --entitlements "$ENTITLEMENTS_PATH" \
        --delete-files "$DELETE_FILES_PATH" \
        --keep-temp-dir  
else
    # 否则使用默认流程（从mobileprovision中提取）
    echo "使用默认entitlements（从mobileprovision中提取）"
    ./onetools ipa resign \
        -p "$IPA_PATH" \
        --bundle-id "$BUNDLE_ID" \
        --provision "$PROVISION_PATH" \
        --cert "$CERT_NAME" \
        --display-name "$DISPLAY_NAME" \
        --app-version "$APP_VERSION" \
        --build-version "$BUILD_VERSION" \
        --app-icon "$APP_ICON" \
        --additional-plist "$ADDITIONAL_PLIST_PATH" \
        --remove-plist-keys "$REMOVE_PLIST_KEYS" \
        --delete-files "$DELETE_FILES_PATH" \
        --add-files-path "$ADD_FILES_PATH" \
        --keep-temp-dir  
fi