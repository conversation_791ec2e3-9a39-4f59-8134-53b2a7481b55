package ipa

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
)

// ListIPAFilesExe 列出IPA文件中的所有文件路径
func ListIPAFilesExe(ipaPath string) error {
	// 创建临时目录
	tempDir, err := ioutil.TempDir("", "ipa_list_*")
	if err != nil {
		return fmt.Errorf("无法创建临时目录: %w", err)
	}

	// 确保在函数结束时删除临时目录
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			fmt.Printf("警告：删除临时目录失败: %v\n", err)
		} else {
			fmt.Printf("临时目录已清理: %s\n", tempDir)
		}
	}()

	fmt.Printf("正在解压IPA文件到临时目录: %s\n", tempDir)

	// 解压IPA文件到临时目录
	err = UnzipIPAExe(ipaPath, tempDir)
	if err != nil {
		return fmt.Errorf("解压IPA文件失败: %w", err)
	}

	fmt.Println("\nIPA文件中包含的所有文件路径：")
	fmt.Println("=" + fmt.Sprintf("%*s", 50, "="))

	// 收集所有文件路径
	var filePaths []string
	err = filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 获取相对于临时目录的路径
		relPath, err := filepath.Rel(tempDir, path)
		if err != nil {
			return err
		}

		// 跳过根目录本身
		if relPath == "." {
			return nil
		}

		// 添加文件类型标识
		if info.IsDir() {
			filePaths = append(filePaths, relPath+"/")
		} else {
			filePaths = append(filePaths, relPath)
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("遍历文件失败: %w", err)
	}

	// 排序文件路径
	sort.Strings(filePaths)

	// 打印所有文件路径
	fileCount := 0
	dirCount := 0
	for _, path := range filePaths {
		fmt.Println(path)
		if path[len(path)-1] == '/' {
			dirCount++
		} else {
			fileCount++
		}
	}

	fmt.Println("=" + fmt.Sprintf("%*s", 50, "="))
	fmt.Printf("统计信息：\n")
	fmt.Printf("  目录数量: %d\n", dirCount)
	fmt.Printf("  文件数量: %d\n", fileCount)
	fmt.Printf("  总计: %d\n", dirCount+fileCount)

	return nil
}
