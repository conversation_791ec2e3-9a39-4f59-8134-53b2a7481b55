package ipa

import (
	"os"
	"path/filepath"
	"testing"
)

// TestParseLibraryInfo 测试库信息解析
func TestParseLibraryInfo(t *testing.T) {
	tests := []struct {
		input    string
		expected *LibraryInfo
		hasError bool
	}{
		{
			input: "OpenDebugLog (framework) -> @rpath/OpenDebugLog.framework/OpenDebugLog",
			expected: &LibraryInfo{
				Name:          "OpenDebugLog",
				Type:          "framework",
				ExpectedRpath: "@rpath/OpenDebugLog.framework/OpenDebugLog",
			},
			hasError: false,
		},
		{
			input: "CustomLib (dylib) -> @rpath/CustomLib.dylib",
			expected: &LibraryInfo{
				Name:          "CustomLib",
				Type:          "dylib",
				ExpectedRpath: "@rpath/CustomLib.dylib",
			},
			hasError: false,
		},
		{
			input:    "InvalidFormat",
			expected: nil,
			hasError: true,
		},
		{
			input:    "NoType -> @rpath/lib.dylib",
			expected: nil,
			hasError: true,
		},
	}

	for _, test := range tests {
		result, err := parseLibraryInfo(test.input)

		if test.hasError {
			if err == nil {
				t.Errorf("Expected error for input: %s", test.input)
			}
			continue
		}

		if err != nil {
			t.Errorf("Unexpected error for input %s: %v", test.input, err)
			continue
		}

		if result.Name != test.expected.Name {
			t.Errorf("Name mismatch for %s: got %s, expected %s",
				test.input, result.Name, test.expected.Name)
		}

		if result.Type != test.expected.Type {
			t.Errorf("Type mismatch for %s: got %s, expected %s",
				test.input, result.Type, test.expected.Type)
		}

		if result.ExpectedRpath != test.expected.ExpectedRpath {
			t.Errorf("ExpectedRpath mismatch for %s: got %s, expected %s",
				test.input, result.ExpectedRpath, test.expected.ExpectedRpath)
		}
	}
}

// TestIsLibraryAlreadyLoaded 测试库加载状态检查
func TestIsLibraryAlreadyLoaded(t *testing.T) {
	info := &RpathInfo{
		LoadedLibraries: []string{
			"/System/Library/Frameworks/Foundation.framework/Foundation",
			"@rpath/LoadedFramework.framework/LoadedFramework",
			"@rpath/LoadedLib.dylib",
		},
	}

	tests := []struct {
		libInfo  *LibraryInfo
		expected bool
	}{
		{
			libInfo: &LibraryInfo{
				Name:          "LoadedFramework",
				Type:          "framework",
				ExpectedRpath: "@rpath/LoadedFramework.framework/LoadedFramework",
			},
			expected: true,
		},
		{
			libInfo: &LibraryInfo{
				Name:          "LoadedLib",
				Type:          "dylib",
				ExpectedRpath: "@rpath/LoadedLib.dylib",
			},
			expected: true,
		},
		{
			libInfo: &LibraryInfo{
				Name:          "UnloadedLib",
				Type:          "dylib",
				ExpectedRpath: "@rpath/UnloadedLib.dylib",
			},
			expected: false,
		},
	}

	for _, test := range tests {
		result := isLibraryAlreadyLoaded(info, test.libInfo)
		if result != test.expected {
			t.Errorf("Library %s: expected %v, got %v",
				test.libInfo.Name, test.expected, result)
		}
	}
}

// TestCheckMissingRpaths 测试缺失rpath检查
func TestCheckMissingRpaths(t *testing.T) {
	info := &RpathInfo{
		RpathList: []string{
			"@executable_path/Frameworks",
			"@loader_path/Frameworks",
		},
		CustomLibraries: []string{
			"Frameworks/TestFramework.framework/TestFramework",
			"Frameworks/TestLib.dylib",
			"CustomPath/CustomLib.dylib",
		},
	}

	missingRpaths := checkMissingRpaths(info)

	// 应该找到一个缺失的rpath: @executable_path/CustomPath
	expectedMissing := []string{"@executable_path/CustomPath"}

	if len(missingRpaths) != len(expectedMissing) {
		t.Errorf("Expected %d missing rpaths, got %d. Missing rpaths: %v",
			len(expectedMissing), len(missingRpaths), missingRpaths)
	}

	for _, expected := range expectedMissing {
		found := false
		for _, missing := range missingRpaths {
			if missing == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected missing rpath %s not found in %v", expected, missingRpaths)
		}
	}
}

// TestCheckMissingRpathsFrameworksOnly 测试只有Frameworks目录的情况
func TestCheckMissingRpathsFrameworksOnly(t *testing.T) {
	info := &RpathInfo{
		RpathList: []string{
			"@executable_path/Frameworks",
		},
		CustomLibraries: []string{
			"Frameworks/TestFramework.framework/TestFramework",
			"Frameworks/TestLib.dylib",
		},
	}

	missingRpaths := checkMissingRpaths(info)

	// 不应该有缺失的rpath，因为所有库都在Frameworks目录下
	if len(missingRpaths) != 0 {
		t.Errorf("Expected 0 missing rpaths, got %d: %v",
			len(missingRpaths), missingRpaths)
	}
}

// TestCopyFileForRpath 测试文件复制功能
func TestCopyFileForRpath(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "rpath_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建源文件
	srcPath := filepath.Join(tempDir, "source.txt")
	srcContent := "test content"
	if err := os.WriteFile(srcPath, []byte(srcContent), 0644); err != nil {
		t.Fatalf("Failed to create source file: %v", err)
	}

	// 复制文件
	dstPath := filepath.Join(tempDir, "destination.txt")
	if err := copyFileForRpath(srcPath, dstPath); err != nil {
		t.Fatalf("Failed to copy file: %v", err)
	}

	// 验证复制结果
	dstContent, err := os.ReadFile(dstPath)
	if err != nil {
		t.Fatalf("Failed to read destination file: %v", err)
	}

	if string(dstContent) != srcContent {
		t.Errorf("Content mismatch: expected %s, got %s",
			srcContent, string(dstContent))
	}

	// 验证文件权限
	srcInfo, _ := os.Stat(srcPath)
	dstInfo, _ := os.Stat(dstPath)

	if srcInfo.Mode() != dstInfo.Mode() {
		t.Errorf("Permission mismatch: expected %v, got %v",
			srcInfo.Mode(), dstInfo.Mode())
	}
}

// TestDetermineMainExecutable 测试主可执行文件确定
func TestDetermineMainExecutable(t *testing.T) {
	// 创建临时目录结构
	tempDir, err := os.MkdirTemp("", "app_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	appPath := filepath.Join(tempDir, "TestApp.app")
	if err := os.MkdirAll(appPath, 0755); err != nil {
		t.Fatalf("Failed to create app directory: %v", err)
	}

	// 测试普通应用
	mainExecPath := filepath.Join(appPath, "TestApp")
	if err := os.WriteFile(mainExecPath, []byte("fake executable"), 0755); err != nil {
		t.Fatalf("Failed to create main executable: %v", err)
	}

	execPath, isUnity, err := determineMainExecutable(appPath)
	if err != nil {
		t.Fatalf("Failed to determine main executable: %v", err)
	}

	if execPath != mainExecPath {
		t.Errorf("Expected main executable %s, got %s", mainExecPath, execPath)
	}

	if isUnity {
		t.Error("Expected non-Unity project, got Unity project")
	}

	// 测试Unity项目
	frameworksDir := filepath.Join(appPath, "Frameworks")
	unityFrameworkDir := filepath.Join(frameworksDir, "UnityFramework.framework")
	if err := os.MkdirAll(unityFrameworkDir, 0755); err != nil {
		t.Fatalf("Failed to create Unity framework directory: %v", err)
	}

	unityExecPath := filepath.Join(unityFrameworkDir, "UnityFramework")
	if err := os.WriteFile(unityExecPath, []byte("fake unity executable"), 0755); err != nil {
		t.Fatalf("Failed to create Unity executable: %v", err)
	}

	execPath, isUnity, err = determineMainExecutable(appPath)
	if err != nil {
		t.Fatalf("Failed to determine Unity main executable: %v", err)
	}

	if execPath != unityExecPath {
		t.Errorf("Expected Unity executable %s, got %s", unityExecPath, execPath)
	}

	if !isUnity {
		t.Error("Expected Unity project, got non-Unity project")
	}
}
