package ipa

import (
	"fmt"
	"io"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
)

// RpathInfo 存储rpath相关信息
type RpathInfo struct {
	AppPath           string   // .app目录路径
	MainExecutable    string   // 主可执行文件路径
	LoadedLibraries   []string // 加载的动态库列表
	RpathList         []string // LC_RPATH列表
	CustomLibraries   []string // 自定义动态库列表
	MissingRpaths     []string // 缺失的rpath
	UnloadedLibraries []string // 未被正确加载的动态库
	LoadedFrameworks  []string // 已正确加载的Framework库
	IsUnityProject    bool     // 是否为Unity项目
}

// 颜色输出定义
const (
	ColorBlue = "\033[34m"
)

// 打印带颜色的信息
func printColorInfoRpath(color, prefix, msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[%s] %s\n", prefix, msg)
	} else {
		fmt.Printf("%s[%s]%s %s\n", color, prefix, ColorNC, msg)
	}
}

func printWarningRpath(msg string) {
	printColorInfoRpath(ColorYellow, "WARNING", msg)
}

// VerifyAndFixRpathExe 验证和修复ipa中的rpath配置
func VerifyAndFixRpathExe(ipaPath string, shouldFix bool) error {
	printInfo(fmt.Sprintf("开始验证IPA文件的rpath配置: %s", ipaPath))

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "rpath_verify_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// 解压IPA文件
	printInfo("解压IPA文件...")
	if err := UnzipIPAExe(ipaPath, tempDir); err != nil {
		return fmt.Errorf("解压IPA文件失败: %w", err)
	}

	// 查找.app目录
	appPath, err := findAppDirectory(tempDir)
	if err != nil {
		return fmt.Errorf("查找.app目录失败: %w", err)
	}

	printInfo(fmt.Sprintf("找到应用目录: %s", filepath.Base(appPath)))

	// 分析rpath信息
	rpathInfo, err := analyzeRpathInfo(appPath)
	if err != nil {
		return fmt.Errorf("分析rpath信息失败: %w", err)
	}

	// 打印分析结果
	printRpathAnalysis(rpathInfo)

	// 如果需要修复且存在问题
	if shouldFix && (len(rpathInfo.MissingRpaths) > 0 || len(rpathInfo.UnloadedLibraries) > 0) {
		printInfo("开始修复rpath配置和动态库注入...")

		// 修复rpath配置
		if err := fixRpathConfiguration(rpathInfo); err != nil {
			return fmt.Errorf("修复rpath配置失败: %w", err)
		}

		// 修复未加载的动态库
		if err := fixUnloadedLibrariesComplete(rpathInfo); err != nil {
			return fmt.Errorf("修复动态库注入失败: %w", err)
		}

		// 验证修复结果
		printInfo("验证修复结果...")
		updatedInfo, err := analyzeRpathInfo(appPath)
		if err != nil {
			return fmt.Errorf("验证修复结果失败: %w", err)
		}

		printInfo("修复后的配置:")
		printRpathAnalysis(updatedInfo)

		// 重新打包IPA
		if err := repackageIPA(tempDir, ipaPath); err != nil {
			return fmt.Errorf("重新打包IPA失败: %w", err)
		}
	}

	return nil
}

// repackageIPA 重新打包IPA
func repackageIPA(tempDir, originalIPAPath string) error {
	// 生成新的IPA文件名
	dir := filepath.Dir(originalIPAPath)
	name := strings.TrimSuffix(filepath.Base(originalIPAPath), ".ipa")
	newIPAPath := filepath.Join(dir, name+"_rpath_fixed.ipa")

	// 创建zip文件
	cmd := exec.Command("zip", "-r", newIPAPath, ".")
	cmd.Dir = tempDir

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("创建IPA文件失败: %w", err)
	}

	printSuccess(fmt.Sprintf("修复后的IPA文件已保存: %s", newIPAPath))
	return nil
}

// findAppDirectory 查找.app目录
func findAppDirectory(tempDir string) (string, error) {
	payloadDir := filepath.Join(tempDir, "Payload")
	if !utility.IsDir(payloadDir) {
		return "", fmt.Errorf("未找到Payload目录")
	}

	entries, err := os.ReadDir(payloadDir)
	if err != nil {
		return "", fmt.Errorf("读取Payload目录失败: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			return filepath.Join(payloadDir, entry.Name()), nil
		}
	}

	return "", fmt.Errorf("未找到.app目录")
}

// analyzeRpathInfo 分析rpath信息
func analyzeRpathInfo(appPath string) (*RpathInfo, error) {
	info := &RpathInfo{
		AppPath: appPath,
	}

	// 确定主可执行文件
	mainExec, isUnity, err := determineMainExecutable(appPath)
	if err != nil {
		return nil, fmt.Errorf("确定主可执行文件失败: %w", err)
	}

	info.MainExecutable = mainExec
	info.IsUnityProject = isUnity

	// 获取加载的动态库列表
	loadedLibs, err := getLoadedLibraries(mainExec)
	if err != nil {
		return nil, fmt.Errorf("获取加载的动态库失败: %w", err)
	}
	info.LoadedLibraries = loadedLibs

	// 获取LC_RPATH列表
	rpathList, err := getRpathList(mainExec)
	if err != nil {
		return nil, fmt.Errorf("获取LC_RPATH列表失败: %w", err)
	}
	info.RpathList = rpathList

	// 扫描自定义动态库
	customLibs, err := scanCustomLibraries(appPath)
	if err != nil {
		return nil, fmt.Errorf("扫描自定义动态库失败: %w", err)
	}
	info.CustomLibraries = customLibs

	// 检查缺失的rpath
	missingRpaths := checkMissingRpaths(info)
	info.MissingRpaths = missingRpaths

	// 检查Frameworks中的动态库加载状态
	unloadedLibs, loadedFrameworks := checkFrameworksLoading(info)
	info.UnloadedLibraries = unloadedLibs
	info.LoadedFrameworks = loadedFrameworks

	return info, nil
}

// determineMainExecutable 确定主可执行文件
func determineMainExecutable(appPath string) (string, bool, error) {
	// 检查是否为Unity项目
	unityFrameworkPath := filepath.Join(appPath, "Frameworks", "UnityFramework.framework", "UnityFramework")
	if utility.IsExist(unityFrameworkPath) {
		return unityFrameworkPath, true, nil
	}

	// 获取应用名称
	appName := strings.TrimSuffix(filepath.Base(appPath), ".app")
	mainExecPath := filepath.Join(appPath, appName)

	if !utility.IsExist(mainExecPath) {
		return "", false, fmt.Errorf("未找到主可执行文件: %s", mainExecPath)
	}

	return mainExecPath, false, nil
}

// getLoadedLibraries 获取加载的动态库列表
func getLoadedLibraries(execPath string) ([]string, error) {
	cmd := exec.Command("otool", "-L", execPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行otool命令失败: %w", err)
	}

	var libraries []string
	lines := strings.Split(string(output), "\n")

	// 跳过第一行（文件路径）
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// 提取库路径（去掉版本信息）
		parts := strings.Fields(line)
		if len(parts) > 0 {
			libPath := parts[0]
			libraries = append(libraries, libPath)
		}
	}

	return libraries, nil
}

// getRpathList 获取LC_RPATH列表
func getRpathList(execPath string) ([]string, error) {
	cmd := exec.Command("otool", "-l", execPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行otool命令失败: %w", err)
	}

	var rpaths []string
	lines := strings.Split(string(output), "\n")

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if strings.Contains(line, "LC_RPATH") {
			// 查找下一行的path信息
			for j := i + 1; j < len(lines) && j < i+5; j++ {
				pathLine := strings.TrimSpace(lines[j])
				if strings.HasPrefix(pathLine, "path ") {
					// 提取路径，格式通常是 "path /path/to/lib (offset 12)"
					re := regexp.MustCompile(`path\s+([^\s]+)`)
					matches := re.FindStringSubmatch(pathLine)
					if len(matches) > 1 {
						rpaths = append(rpaths, matches[1])
					}
					break
				}
			}
		}
	}

	return rpaths, nil
}

// scanCustomLibraries 扫描自定义动态库
func scanCustomLibraries(appPath string) ([]string, error) {
	var customLibs []string

	frameworksPath := filepath.Join(appPath, "Frameworks")
	if !utility.IsDir(frameworksPath) {
		return customLibs, nil
	}

	err := filepath.Walk(frameworksPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 收集.dylib文件
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".dylib") {
			relPath, _ := filepath.Rel(appPath, path)
			customLibs = append(customLibs, relPath)
		}

		// 收集.framework中的可执行文件
		if info.IsDir() && strings.HasSuffix(info.Name(), ".framework") {
			frameworkName := strings.TrimSuffix(info.Name(), ".framework")
			execPath := filepath.Join(path, frameworkName)
			if utility.IsExist(execPath) {
				relPath, _ := filepath.Rel(appPath, execPath)
				customLibs = append(customLibs, relPath)
			}
		}

		return nil
	})

	return customLibs, err
}

// checkMissingRpaths 检查缺失的rpath和未正确加载的动态库
func checkMissingRpaths(info *RpathInfo) []string {
	var missingRpaths []string

	// 检查每个自定义动态库是否有对应的rpath
	for _, customLib := range info.CustomLibraries {
		libDir := filepath.Dir(customLib)

		// 转换为rpath格式
		var expectedRpath string
		if strings.HasPrefix(libDir, "Frameworks") {
			expectedRpath = "@executable_path/Frameworks"
		} else {
			expectedRpath = "@executable_path/" + libDir
		}

		// 检查是否已存在对应的rpath
		found := false
		for _, existingRpath := range info.RpathList {
			if existingRpath == expectedRpath ||
				existingRpath == "@executable_path/Frameworks" ||
				existingRpath == "@loader_path/Frameworks" {
				found = true
				break
			}
		}

		if !found {
			// 避免重复添加
			duplicate := false
			for _, missing := range missingRpaths {
				if missing == expectedRpath {
					duplicate = true
					break
				}
			}
			if !duplicate {
				missingRpaths = append(missingRpaths, expectedRpath)
			}
		}
	}

	return missingRpaths
}

// checkFrameworksLoading 检查Frameworks目录中的动态库是否被主进程正确加载
func checkFrameworksLoading(info *RpathInfo) ([]string, []string) {
	var unloadedLibraries []string
	var loadedLibraries []string

	frameworksPath := filepath.Join(info.AppPath, "Frameworks")
	if !utility.IsDir(frameworksPath) {
		return unloadedLibraries, loadedLibraries
	}

	// printInfo("检查Frameworks目录中的动态库加载状态...")

	// 遍历Frameworks目录
	filepath.Walk(frameworksPath, func(path string, fileInfo os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		var libName string
		var expectedRpathRef string
		var libType string

		// 处理.dylib文件
		if !fileInfo.IsDir() && strings.HasSuffix(fileInfo.Name(), ".dylib") {
			// 跳过libswift开头且以.dylib结尾的系统动态库
			if strings.HasPrefix(fileInfo.Name(), "libswift") && strings.HasSuffix(fileInfo.Name(), ".dylib") {
				// printColorInfoRpath(ColorBlue, "SKIP", fmt.Sprintf("跳过系统Swift库: %s", fileInfo.Name()))
				return nil
			}

			libName = fileInfo.Name()
			expectedRpathRef = "@rpath/" + libName
			libType = "dylib"
		}

		// 处理.framework目录
		if fileInfo.IsDir() && strings.HasSuffix(fileInfo.Name(), ".framework") {
			frameworkName := strings.TrimSuffix(fileInfo.Name(), ".framework")
			execPath := filepath.Join(path, frameworkName)

			// 检查framework内的可执行文件是否存在
			if utility.IsExist(execPath) {
				// 检查是否为可执行文件（不是符号链接或其他类型）
				if fileInfo, err := os.Stat(execPath); err == nil {
					if fileInfo.Mode().IsRegular() && (fileInfo.Mode()&0111) != 0 {
						libName = frameworkName
						expectedRpathRef = "@rpath/" + fileInfo.Name() + "/" + frameworkName
						libType = "framework"
					}
				}
			}
		}

		// 如果找到了需要检查的库
		if libName != "" && expectedRpathRef != "" {
			// printColorInfoRpath(ColorBlue, "CHECK", fmt.Sprintf("检查%s: %s", libType, libName))

			// 检查是否在主进程的加载列表中
			isLoaded := false
			for _, loadedLib := range info.LoadedLibraries {
				// 精确匹配或模糊匹配
				if loadedLib == expectedRpathRef ||
					strings.Contains(loadedLib, libName) ||
					(libType == "framework" && strings.Contains(loadedLib, fmt.Sprintf("%s.framework/%s", libName, libName))) {
					isLoaded = true
					break
				}
			}

			resultMsg := fmt.Sprintf("%s (%s) -> %s", libName, libType, expectedRpathRef)

			if isLoaded {
				loadedLibraries = append(loadedLibraries, resultMsg)
				// printSuccess(fmt.Sprintf("✓ 已加载: %s", resultMsg))
			} else {
				unloadedLibraries = append(unloadedLibraries, resultMsg)
				// printError(fmt.Sprintf("✗ 未加载: %s", resultMsg))
			}
		}

		return nil
	})

	return unloadedLibraries, loadedLibraries
}

// printRpathAnalysis 打印rpath分析结果
func printRpathAnalysis(info *RpathInfo) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("RPATH 配置分析结果")
	fmt.Println(strings.Repeat("=", 60))

	// 打印基本信息
	fmt.Printf("应用目录: %s\n", filepath.Base(info.AppPath))
	fmt.Printf("主可执行文件: %s\n", filepath.Base(info.MainExecutable))
	if info.IsUnityProject {
		printInfo("检测到Unity项目，使用UnityFramework作为主可执行文件")
	}

	// 打印加载的动态库
	fmt.Println("\n1. 主要加载的动态库:")
	if len(info.LoadedLibraries) == 0 {
		printWarningRpath("未找到加载的动态库")
	} else {
		for i, lib := range info.LoadedLibraries {
			fmt.Printf("   %d. %s\n", i+1, lib)
		}
	}

	// 打印LC_RPATH列表
	fmt.Println("\n2. LC_RPATH 路径:")
	if len(info.RpathList) == 0 {
		printWarningRpath("未找到LC_RPATH配置")
	} else {
		for i, rpath := range info.RpathList {
			fmt.Printf("   %d. %s\n", i+1, rpath)
		}
	}

	// 打印自定义动态库
	fmt.Println("\n3. 自定义动态库:")
	if len(info.CustomLibraries) == 0 {
		printInfo("未找到自定义动态库")
	} else {
		for i, lib := range info.CustomLibraries {
			fmt.Printf("   %d. %s\n", i+1, lib)
		}
	}

	// 打印缺失的rpath
	fmt.Println("\n4. 缺失的RPATH配置:")
	if len(info.MissingRpaths) == 0 {
		printSuccess("所有自定义动态库的rpath配置正确")
	} else {
		for i, rpath := range info.MissingRpaths {
			printError(fmt.Sprintf("%d. %s", i+1, rpath))
		}
	}

	// 打印未正确加载的动态库
	fmt.Println("\n5. 未正确加载的动态库:")
	if len(info.UnloadedLibraries) == 0 {
		printSuccess("所有Frameworks中的动态库都已被主进程正确加载")
	} else {
		printWarningRpath(fmt.Sprintf("发现 %d 个未被主进程加载的动态库:", len(info.UnloadedLibraries)))
		for i, lib := range info.UnloadedLibraries {
			printError(fmt.Sprintf("%d. %s", i+1, lib))
		}
		printWarningRpath("这些库可能导致运行时加载失败，建议检查代码中的动态库引用")
	}

	fmt.Println(strings.Repeat("=", 60))
}

// fixRpathConfiguration 修复rpath配置
func fixRpathConfiguration(info *RpathInfo) error {
	if len(info.MissingRpaths) == 0 {
		return nil
	}

	printInfo(fmt.Sprintf("需要添加 %d 个rpath配置", len(info.MissingRpaths)))

	for _, rpath := range info.MissingRpaths {
		printInfo(fmt.Sprintf("添加rpath: %s", rpath))

		// 使用install_name_tool添加rpath
		cmd := exec.Command("install_name_tool", "-add_rpath", rpath, info.MainExecutable)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("添加rpath失败 %s: %w", rpath, err)
		}

		printSuccess(fmt.Sprintf("成功添加rpath: %s", rpath))
	}

	return nil
}

// fixUnloadedLibraries 修复未加载的动态库，将它们添加到主进程的Load command中
func fixUnloadedLibraries(info *RpathInfo) error {
	if len(info.UnloadedLibraries) == 0 {
		return nil
	}

	printInfo(fmt.Sprintf("需要修复 %d 个未加载的动态库", len(info.UnloadedLibraries)))

	for _, unloadedLib := range info.UnloadedLibraries {
		// 解析库信息，格式: "OpenDebugLog (framework) -> @rpath/OpenDebugLog/OpenDebugLog"
		parts := strings.Split(unloadedLib, " -> ")
		if len(parts) != 2 {
			printWarningRpath(fmt.Sprintf("跳过格式不正确的库信息: %s", unloadedLib))
			continue
		}

		libInfo := parts[0]
		expectedRpathRef := parts[1]

		// 提取库名和类型
		libNameParts := strings.Split(libInfo, " (")
		if len(libNameParts) != 2 {
			printWarningRpath(fmt.Sprintf("跳过格式不正确的库信息: %s", libInfo))
			continue
		}

		libName := libNameParts[0]
		libType := strings.TrimSuffix(libNameParts[1], ")")

		printInfo(fmt.Sprintf("修复未加载的%s: %s", libType, libName))

		// 构建实际的库文件路径
		var actualLibPath string
		if libType == "framework" {
			// 对于framework，路径格式为: Frameworks/LibName.framework/LibName
			actualLibPath = filepath.Join(info.AppPath, "Frameworks", libName+".framework", libName)
		} else if libType == "dylib" {
			// 对于dylib，路径格式为: Frameworks/libname.dylib
			actualLibPath = filepath.Join(info.AppPath, "Frameworks", libName)
		} else {
			printWarningRpath(fmt.Sprintf("不支持的库类型: %s", libType))
			continue
		}

		// 检查库文件是否存在
		if !utility.IsExist(actualLibPath) {
			printWarningRpath(fmt.Sprintf("库文件不存在，跳过: %s", actualLibPath))
			continue
		}

		// 使用install_name_tool添加库到主进程的Load command中
		printInfo(fmt.Sprintf("将 %s 添加到主进程Load command中...", expectedRpathRef))

		// 使用-change参数可能不适用，我们需要使用-add_load_command或者其他方法
		// 但install_name_tool没有直接添加load command的功能
		// 我们需要使用其他方法，比如修改二进制文件或者重新链接

		// 作为替代方案，我们可以检查是否可以通过修改库的install_name来解决
		// 首先获取库的当前install_name
		currentInstallName, err := getLibraryInstallName(actualLibPath)
		if err != nil {
			printWarningRpath(fmt.Sprintf("获取库install_name失败: %v", err))
			continue
		}

		printInfo(fmt.Sprintf("当前库install_name: %s", currentInstallName))
		printInfo(fmt.Sprintf("期望的引用路径: %s", expectedRpathRef))

		// 如果当前install_name与期望的引用路径不匹配，尝试修改
		if currentInstallName != expectedRpathRef {
			printInfo(fmt.Sprintf("修改库的install_name从 %s 到 %s", currentInstallName, expectedRpathRef))

			cmd := exec.Command("install_name_tool", "-id", expectedRpathRef, actualLibPath)
			if err := cmd.Run(); err != nil {
				printWarningRpath(fmt.Sprintf("修改库install_name失败: %v", err))
				continue
			}

			printSuccess(fmt.Sprintf("成功修改库install_name: %s", libName))
		}

		// 注意：仅修改install_name可能不足以解决问题
		// 如果主进程确实没有加载这个库，可能需要在代码层面添加对该库的引用
		// 或者使用其他工具来修改主进程的Load Commands
		printWarningRpath(fmt.Sprintf("已修改库 %s 的install_name，但如果主进程代码中没有引用该库，仍可能无法加载", libName))
		printWarningRpath("建议检查代码中是否正确引用了该库，或考虑使用dlopen等动态加载方式")
	}

	return nil
}

// getLibraryInstallName 获取库的install_name
func getLibraryInstallName(libPath string) (string, error) {
	cmd := exec.Command("otool", "-D", libPath)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行otool -D命令失败: %w", err)
	}

	lines := strings.Split(string(output), "\n")
	// 第一行是文件路径，第二行是install_name
	if len(lines) >= 2 {
		installName := strings.TrimSpace(lines[1])
		if installName != "" {
			return installName, nil
		}
	}

	return "", fmt.Errorf("未找到install_name")
}

// fixUnloadedLibrariesComplete 完整的动态库注入解决方案
func fixUnloadedLibrariesComplete(info *RpathInfo) error {
	if len(info.UnloadedLibraries) == 0 {
		return nil
	}

	printInfo(fmt.Sprintf("开始完整的动态库注入流程，需要处理 %d 个未加载的动态库", len(info.UnloadedLibraries)))

	for _, unloadedLib := range info.UnloadedLibraries {
		// 解析库信息
		libInfo, err := parseLibraryInfo(unloadedLib)
		if err != nil {
			printWarningRpath(fmt.Sprintf("跳过格式不正确的库信息: %s, 错误: %v", unloadedLib, err))
			continue
		}

		printInfo(fmt.Sprintf("处理未加载的%s: %s", libInfo.Type, libInfo.Name))

		// 步骤1: 修复库的install_name
		if err := fixLibraryInstallName(info, libInfo); err != nil {
			printWarningRpath(fmt.Sprintf("修复库install_name失败: %v", err))
			continue
		}

		// 步骤2: 将库添加到主进程的Load Commands中
		if err := injectLibraryToMainExecutable(info, libInfo); err != nil {
			printWarningRpath(fmt.Sprintf("注入库到主进程失败: %v", err))
			continue
		}

		printSuccess(fmt.Sprintf("成功完成动态库注入: %s", libInfo.Name))
	}

	return nil
}

// LibraryInfo 库信息结构
type LibraryInfo struct {
	Name          string // 库名称
	Type          string // 库类型: framework 或 dylib
	ExpectedRpath string // 期望的rpath引用
	ActualPath    string // 实际文件路径
}

// parseLibraryInfo 解析库信息字符串
func parseLibraryInfo(unloadedLib string) (*LibraryInfo, error) {
	// 格式: "OpenDebugLog (framework) -> @rpath/OpenDebugLog/OpenDebugLog"
	parts := strings.Split(unloadedLib, " -> ")
	if len(parts) != 2 {
		return nil, fmt.Errorf("格式不正确，期望格式: 'name (type) -> rpath'")
	}

	libInfo := parts[0]
	expectedRpathRef := parts[1]

	// 提取库名和类型
	libNameParts := strings.Split(libInfo, " (")
	if len(libNameParts) != 2 {
		return nil, fmt.Errorf("库信息格式不正确: %s", libInfo)
	}

	libName := libNameParts[0]
	libType := strings.TrimSuffix(libNameParts[1], ")")

	return &LibraryInfo{
		Name:          libName,
		Type:          libType,
		ExpectedRpath: expectedRpathRef,
	}, nil
}

// fixLibraryInstallName 修复库的install_name
func fixLibraryInstallName(info *RpathInfo, libInfo *LibraryInfo) error {
	// 构建实际的库文件路径
	var actualLibPath string
	if libInfo.Type == "framework" {
		actualLibPath = filepath.Join(info.AppPath, "Frameworks", libInfo.Name+".framework", libInfo.Name)
	} else if libInfo.Type == "dylib" {
		actualLibPath = filepath.Join(info.AppPath, "Frameworks", libInfo.Name)
	} else {
		return fmt.Errorf("不支持的库类型: %s", libInfo.Type)
	}

	libInfo.ActualPath = actualLibPath

	// 检查库文件是否存在
	if !utility.IsExist(actualLibPath) {
		return fmt.Errorf("库文件不存在: %s", actualLibPath)
	}

	// 获取当前install_name
	currentInstallName, err := getLibraryInstallName(actualLibPath)
	if err != nil {
		return fmt.Errorf("获取库install_name失败: %w", err)
	}

	printInfo(fmt.Sprintf("当前库install_name: %s", currentInstallName))
	printInfo(fmt.Sprintf("期望的引用路径: %s", libInfo.ExpectedRpath))

	// 如果当前install_name与期望的引用路径不匹配，修改它
	if currentInstallName != libInfo.ExpectedRpath {
		printInfo(fmt.Sprintf("修改库的install_name从 %s 到 %s", currentInstallName, libInfo.ExpectedRpath))

		cmd := exec.Command("install_name_tool", "-id", libInfo.ExpectedRpath, actualLibPath)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("修改库install_name失败: %w", err)
		}

		printSuccess(fmt.Sprintf("成功修改库install_name: %s", libInfo.Name))
	} else {
		printInfo(fmt.Sprintf("库install_name已正确: %s", libInfo.Name))
	}

	return nil
}

// injectLibraryToMainExecutable 将库注入到主进程的Load Commands中
func injectLibraryToMainExecutable(info *RpathInfo, libInfo *LibraryInfo) error {
	printInfo(fmt.Sprintf("将库 %s 注入到主进程Load Commands中...", libInfo.Name))

	// 检查库是否已经被主进程加载
	if isLibraryAlreadyLoaded(info, libInfo) {
		printInfo(fmt.Sprintf("库 %s 已经被主进程加载，跳过注入", libInfo.Name))
		return nil
	}

	// 使用install_name_tool添加库依赖
	// 注意：install_name_tool无法直接添加新的load command，我们需要使用其他方法

	// 方法1: 尝试使用optool（如果可用）
	if err := injectUsingOptool(info.MainExecutable, libInfo.ExpectedRpath); err == nil {
		printSuccess(fmt.Sprintf("使用optool成功注入库: %s", libInfo.Name))
		return nil
	}

	// 方法2: 使用insert_dylib（如果可用）
	if err := injectUsingInsertDylib(info.MainExecutable, libInfo.ExpectedRpath); err == nil {
		printSuccess(fmt.Sprintf("使用insert_dylib成功注入库: %s", libInfo.Name))
		return nil
	}

	// 方法3: 创建一个包装器来强制加载库
	if err := createLibraryWrapper(info, libInfo); err == nil {
		printSuccess(fmt.Sprintf("使用包装器方法成功处理库: %s", libInfo.Name))
		return nil
	}

	// 如果所有方法都失败，提供手动解决方案建议
	printWarningRpath(fmt.Sprintf("无法自动注入库 %s，请考虑以下解决方案:", libInfo.Name))
	printWarningRpath("1. 在代码中添加对该库的显式引用")
	printWarningRpath("2. 使用dlopen()动态加载库")
	printWarningRpath("3. 安装optool或insert_dylib工具进行手动注入")

	return fmt.Errorf("无法自动注入库 %s，需要手动处理", libInfo.Name)
}

// isLibraryAlreadyLoaded 检查库是否已经被主进程加载
func isLibraryAlreadyLoaded(info *RpathInfo, libInfo *LibraryInfo) bool {
	for _, loadedLib := range info.LoadedLibraries {
		if loadedLib == libInfo.ExpectedRpath ||
			strings.Contains(loadedLib, libInfo.Name) ||
			(libInfo.Type == "framework" && strings.Contains(loadedLib, fmt.Sprintf("%s.framework/%s", libInfo.Name, libInfo.Name))) {
			return true
		}
	}
	return false
}

// injectUsingOptool 使用optool工具注入库
func injectUsingOptool(mainExec, libraryPath string) error {
	// 检查optool是否可用
	if _, err := exec.LookPath("optool"); err != nil {
		return fmt.Errorf("optool工具不可用: %w", err)
	}

	printInfo("使用optool注入库...")
	cmd := exec.Command("optool", "install", "-c", "load", "-p", libraryPath, "-t", mainExec)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("optool执行失败: %w, 输出: %s", err, string(output))
	}

	return nil
}

// injectUsingInsertDylib 使用insert_dylib工具注入库
func injectUsingInsertDylib(mainExec, libraryPath string) error {
	// 检查insert_dylib是否可用
	if _, err := exec.LookPath("insert_dylib"); err != nil {
		return fmt.Errorf("insert_dylib工具不可用: %w", err)
	}

	printInfo("使用insert_dylib注入库...")

	// 创建备份
	backupPath := mainExec + ".backup"
	if err := copyFileForRpath(mainExec, backupPath); err != nil {
		return fmt.Errorf("创建备份失败: %w", err)
	}

	// 使用insert_dylib注入
	cmd := exec.Command("insert_dylib", libraryPath, mainExec, mainExec)
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 恢复备份
		os.Rename(backupPath, mainExec)
		return fmt.Errorf("insert_dylib执行失败: %w, 输出: %s", err, string(output))
	}

	// 删除备份
	os.Remove(backupPath)
	return nil
}

// copyFileForRpath 复制文件（避免与其他包中的copyFile冲突）
func copyFileForRpath(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	sourceInfo, err := os.Stat(src)
	if err != nil {
		return err
	}
	return os.Chmod(dst, sourceInfo.Mode())
}

// createLibraryWrapper 创建库包装器来强制加载
func createLibraryWrapper(info *RpathInfo, libInfo *LibraryInfo) error {
	printInfo(fmt.Sprintf("为库 %s 创建加载包装器...", libInfo.Name))

	// 这是一个替代方案：创建一个小的动态库来强制加载目标库
	// 然后将这个包装器库添加到主进程中

	wrapperDir := filepath.Join(info.AppPath, "Frameworks", "LibraryWrappers")
	if err := os.MkdirAll(wrapperDir, 0755); err != nil {
		return fmt.Errorf("创建包装器目录失败: %w", err)
	}

	wrapperName := fmt.Sprintf("lib%sWrapper.dylib", libInfo.Name)
	wrapperPath := filepath.Join(wrapperDir, wrapperName)

	// 创建一个简单的C源文件来强制链接目标库
	cSourcePath := filepath.Join(wrapperDir, fmt.Sprintf("%sWrapper.c", libInfo.Name))
	cSource := fmt.Sprintf(`
// Auto-generated wrapper for %s
#include <dlfcn.h>
#include <stdio.h>

__attribute__((constructor))
void load_%s_library() {
    void* handle = dlopen("%s", RTLD_LAZY);
    if (!handle) {
        printf("Warning: Failed to load %s: %%s\\n", dlerror());
    }
}
`, libInfo.Name, libInfo.Name, libInfo.ExpectedRpath, libInfo.Name)

	if err := os.WriteFile(cSourcePath, []byte(cSource), 0644); err != nil {
		return fmt.Errorf("创建C源文件失败: %w", err)
	}

	// 编译包装器库
	cmd := exec.Command("clang", "-shared", "-fPIC", "-o", wrapperPath, cSourcePath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("编译包装器库失败: %w", err)
	}

	// 设置包装器库的install_name
	wrapperRpath := fmt.Sprintf("@rpath/LibraryWrappers/%s", wrapperName)
	cmd = exec.Command("install_name_tool", "-id", wrapperRpath, wrapperPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("设置包装器库install_name失败: %w", err)
	}

	// 清理临时文件
	os.Remove(cSourcePath)

	printInfo(fmt.Sprintf("包装器库已创建: %s", wrapperRpath))
	printWarningRpath("注意：包装器库方法需要在代码中引用包装器库才能生效")

	return nil
}
