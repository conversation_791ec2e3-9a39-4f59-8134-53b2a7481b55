package ipa

import (
	"fmt"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
)

// RpathInfo 存储rpath相关信息
type RpathInfo struct {
	AppPath           string   // .app目录路径
	MainExecutable    string   // 主可执行文件路径
	LoadedLibraries   []string // 加载的动态库列表
	RpathList         []string // LC_RPATH列表
	CustomLibraries   []string // 自定义动态库列表
	MissingRpaths     []string // 缺失的rpath
	UnloadedLibraries []string // 未被正确加载的动态库
	LoadedFrameworks  []string // 已正确加载的Framework库
	IsUnityProject    bool     // 是否为Unity项目
}

// 颜色输出定义
const (
	ColorBlue = "\033[34m"
)

// 打印带颜色的信息
func printColorInfoRpath(color, prefix, msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[%s] %s\n", prefix, msg)
	} else {
		fmt.Printf("%s[%s]%s %s\n", color, prefix, ColorNC, msg)
	}
}

func printWarningRpath(msg string) {
	printColorInfoRpath(ColorYellow, "WARNING", msg)
}

// VerifyAndFixRpathExe 验证和修复ipa中的rpath配置
func VerifyAndFixRpathExe(ipaPath string) error {
	printInfo(fmt.Sprintf("开始验证IPA文件的rpath配置: %s", ipaPath))

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "rpath_verify_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// 解压IPA文件
	printInfo("解压IPA文件...")
	if err := UnzipIPAExe(ipaPath, tempDir); err != nil {
		return fmt.Errorf("解压IPA文件失败: %w", err)
	}

	// 查找.app目录
	appPath, err := findAppDirectory(tempDir)
	if err != nil {
		return fmt.Errorf("查找.app目录失败: %w", err)
	}

	printInfo(fmt.Sprintf("找到应用目录: %s", filepath.Base(appPath)))

	// 分析rpath信息
	rpathInfo, err := analyzeRpathInfo(appPath)
	if err != nil {
		return fmt.Errorf("分析rpath信息失败: %w", err)
	}

	// 打印分析结果
	printRpathAnalysis(rpathInfo)
	return nil
}

// repackageIPA 重新打包IPA
func repackageIPA(tempDir, originalIPAPath string) error {
	// 生成新的IPA文件名
	dir := filepath.Dir(originalIPAPath)
	name := strings.TrimSuffix(filepath.Base(originalIPAPath), ".ipa")
	newIPAPath := filepath.Join(dir, name+"_rpath_fixed.ipa")

	// 创建zip文件
	cmd := exec.Command("zip", "-r", newIPAPath, ".")
	cmd.Dir = tempDir

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("创建IPA文件失败: %w", err)
	}

	printSuccess(fmt.Sprintf("修复后的IPA文件已保存: %s", newIPAPath))
	return nil
}

// findAppDirectory 查找.app目录
func findAppDirectory(tempDir string) (string, error) {
	payloadDir := filepath.Join(tempDir, "Payload")
	if !utility.IsDir(payloadDir) {
		return "", fmt.Errorf("未找到Payload目录")
	}

	entries, err := os.ReadDir(payloadDir)
	if err != nil {
		return "", fmt.Errorf("读取Payload目录失败: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			return filepath.Join(payloadDir, entry.Name()), nil
		}
	}

	return "", fmt.Errorf("未找到.app目录")
}

// analyzeRpathInfo 分析rpath信息
func analyzeRpathInfo(appPath string) (*RpathInfo, error) {
	info := &RpathInfo{
		AppPath: appPath,
	}

	// 确定主可执行文件
	mainExec, isUnity, err := determineMainExecutable(appPath)
	if err != nil {
		return nil, fmt.Errorf("确定主可执行文件失败: %w", err)
	}

	info.MainExecutable = mainExec
	info.IsUnityProject = isUnity

	// 获取加载的动态库列表
	loadedLibs, err := getLoadedLibraries(mainExec)
	if err != nil {
		return nil, fmt.Errorf("获取加载的动态库失败: %w", err)
	}
	info.LoadedLibraries = loadedLibs

	// 获取LC_RPATH列表
	rpathList, err := getRpathList(mainExec)
	if err != nil {
		return nil, fmt.Errorf("获取LC_RPATH列表失败: %w", err)
	}
	info.RpathList = rpathList

	// 扫描自定义动态库
	customLibs, err := scanCustomLibraries(appPath)
	if err != nil {
		return nil, fmt.Errorf("扫描自定义动态库失败: %w", err)
	}
	info.CustomLibraries = customLibs

	// 检查缺失的rpath
	missingRpaths := checkMissingRpaths(info)
	info.MissingRpaths = missingRpaths

	// 检查Frameworks中的动态库加载状态
	unloadedLibs, loadedFrameworks := checkFrameworksLoading(info)
	info.UnloadedLibraries = unloadedLibs
	info.LoadedFrameworks = loadedFrameworks

	return info, nil
}

// determineMainExecutable 确定主可执行文件
func determineMainExecutable(appPath string) (string, bool, error) {
	// 检查是否为Unity项目
	unityFrameworkPath := filepath.Join(appPath, "Frameworks", "UnityFramework.framework", "UnityFramework")
	if utility.IsExist(unityFrameworkPath) {
		return unityFrameworkPath, true, nil
	}

	// 获取应用名称
	appName := strings.TrimSuffix(filepath.Base(appPath), ".app")
	mainExecPath := filepath.Join(appPath, appName)

	if !utility.IsExist(mainExecPath) {
		return "", false, fmt.Errorf("未找到主可执行文件: %s", mainExecPath)
	}

	return mainExecPath, false, nil
}

// getLoadedLibraries 获取加载的动态库列表
func getLoadedLibraries(execPath string) ([]string, error) {
	cmd := exec.Command("otool", "-L", execPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行otool命令失败: %w", err)
	}

	var libraries []string
	lines := strings.Split(string(output), "\n")

	// 跳过第一行（文件路径）
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// 提取库路径（去掉版本信息）
		parts := strings.Fields(line)
		if len(parts) > 0 {
			libPath := parts[0]
			libraries = append(libraries, libPath)
		}
	}

	return libraries, nil
}

// getRpathList 获取LC_RPATH列表
func getRpathList(execPath string) ([]string, error) {
	cmd := exec.Command("otool", "-l", execPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行otool命令失败: %w", err)
	}

	var rpaths []string
	lines := strings.Split(string(output), "\n")

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if strings.Contains(line, "LC_RPATH") {
			// 查找下一行的path信息
			for j := i + 1; j < len(lines) && j < i+5; j++ {
				pathLine := strings.TrimSpace(lines[j])
				if strings.HasPrefix(pathLine, "path ") {
					// 提取路径，格式通常是 "path /path/to/lib (offset 12)"
					re := regexp.MustCompile(`path\s+([^\s]+)`)
					matches := re.FindStringSubmatch(pathLine)
					if len(matches) > 1 {
						rpaths = append(rpaths, matches[1])
					}
					break
				}
			}
		}
	}

	return rpaths, nil
}

// scanCustomLibraries 扫描自定义动态库
func scanCustomLibraries(appPath string) ([]string, error) {
	var customLibs []string

	frameworksPath := filepath.Join(appPath, "Frameworks")
	if !utility.IsDir(frameworksPath) {
		return customLibs, nil
	}

	err := filepath.Walk(frameworksPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 收集.dylib文件
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".dylib") {
			relPath, _ := filepath.Rel(appPath, path)
			customLibs = append(customLibs, relPath)
		}

		// 收集.framework中的可执行文件
		if info.IsDir() && strings.HasSuffix(info.Name(), ".framework") {
			frameworkName := strings.TrimSuffix(info.Name(), ".framework")
			execPath := filepath.Join(path, frameworkName)
			if utility.IsExist(execPath) {
				relPath, _ := filepath.Rel(appPath, execPath)
				customLibs = append(customLibs, relPath)
			}
		}

		return nil
	})

	return customLibs, err
}

// checkMissingRpaths 检查缺失的rpath和未正确加载的动态库
func checkMissingRpaths(info *RpathInfo) []string {
	var missingRpaths []string

	// 检查每个自定义动态库是否有对应的rpath
	for _, customLib := range info.CustomLibraries {
		libDir := filepath.Dir(customLib)

		// 转换为rpath格式
		var expectedRpath string
		if strings.HasPrefix(libDir, "Frameworks") {
			expectedRpath = "@executable_path/Frameworks"
		} else {
			expectedRpath = "@executable_path/" + libDir
		}

		// 检查是否已存在对应的rpath
		found := false
		for _, existingRpath := range info.RpathList {
			if existingRpath == expectedRpath ||
				existingRpath == "@executable_path/Frameworks" ||
				existingRpath == "@loader_path/Frameworks" {
				found = true
				break
			}
		}

		if !found {
			// 避免重复添加
			duplicate := false
			for _, missing := range missingRpaths {
				if missing == expectedRpath {
					duplicate = true
					break
				}
			}
			if !duplicate {
				missingRpaths = append(missingRpaths, expectedRpath)
			}
		}
	}

	return missingRpaths
}

// checkFrameworksLoading 检查Frameworks目录中的动态库是否被主进程正确加载
func checkFrameworksLoading(info *RpathInfo) ([]string, []string) {
	var unloadedLibraries []string
	var loadedLibraries []string

	frameworksPath := filepath.Join(info.AppPath, "Frameworks")
	if !utility.IsDir(frameworksPath) {
		return unloadedLibraries, loadedLibraries
	}

	// printInfo("检查Frameworks目录中的动态库加载状态...")

	// 遍历Frameworks目录
	filepath.Walk(frameworksPath, func(path string, fileInfo os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		var libName string
		var expectedRpathRef string
		var libType string

		// 处理.dylib文件
		if !fileInfo.IsDir() && strings.HasSuffix(fileInfo.Name(), ".dylib") {
			// 跳过libswift开头且以.dylib结尾的系统动态库
			if strings.HasPrefix(fileInfo.Name(), "libswift") && strings.HasSuffix(fileInfo.Name(), ".dylib") {
				// printColorInfoRpath(ColorBlue, "SKIP", fmt.Sprintf("跳过系统Swift库: %s", fileInfo.Name()))
				return nil
			}

			libName = fileInfo.Name()
			expectedRpathRef = "@rpath/" + libName
			libType = "dylib"
		}

		// 处理.framework目录
		if fileInfo.IsDir() && strings.HasSuffix(fileInfo.Name(), ".framework") {
			frameworkName := strings.TrimSuffix(fileInfo.Name(), ".framework")
			execPath := filepath.Join(path, frameworkName)

			// 检查framework内的可执行文件是否存在
			if utility.IsExist(execPath) {
				// 检查是否为可执行文件（不是符号链接或其他类型）
				if fileInfo, err := os.Stat(execPath); err == nil {
					if fileInfo.Mode().IsRegular() && (fileInfo.Mode()&0111) != 0 {
						libName = frameworkName
						expectedRpathRef = "@rpath/" + fileInfo.Name() + "/" + frameworkName
						libType = "framework"
					}
				}
			}
		}

		// 如果找到了需要检查的库
		if libName != "" && expectedRpathRef != "" {
			// printColorInfoRpath(ColorBlue, "CHECK", fmt.Sprintf("检查%s: %s", libType, libName))

			// 检查是否在主进程的加载列表中
			isLoaded := false
			for _, loadedLib := range info.LoadedLibraries {
				// 精确匹配或模糊匹配
				if loadedLib == expectedRpathRef ||
					strings.Contains(loadedLib, libName) ||
					(libType == "framework" && strings.Contains(loadedLib, fmt.Sprintf("%s.framework/%s", libName, libName))) {
					isLoaded = true
					break
				}
			}

			resultMsg := fmt.Sprintf("%s (%s) -> %s", libName, libType, expectedRpathRef)

			if isLoaded {
				loadedLibraries = append(loadedLibraries, resultMsg)
				// printSuccess(fmt.Sprintf("✓ 已加载: %s", resultMsg))
			} else {
				unloadedLibraries = append(unloadedLibraries, resultMsg)
				// printError(fmt.Sprintf("✗ 未加载: %s", resultMsg))
			}
		}

		return nil
	})

	return unloadedLibraries, loadedLibraries
}

// printRpathAnalysis 打印rpath分析结果
func printRpathAnalysis(info *RpathInfo) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("RPATH 配置分析结果")
	fmt.Println(strings.Repeat("=", 60))

	// 打印基本信息
	fmt.Printf("应用目录: %s\n", filepath.Base(info.AppPath))
	fmt.Printf("主可执行文件: %s\n", filepath.Base(info.MainExecutable))
	if info.IsUnityProject {
		printInfo("检测到Unity项目，使用UnityFramework作为主可执行文件")
	}

	// 打印加载的动态库
	fmt.Println("\n1. 主要加载的动态库:")
	if len(info.LoadedLibraries) == 0 {
		printWarningRpath("未找到加载的动态库")
	} else {
		for i, lib := range info.LoadedLibraries {
			fmt.Printf("   %d. %s\n", i+1, lib)
		}
	}

	// 打印LC_RPATH列表
	fmt.Println("\n2. LC_RPATH 路径:")
	if len(info.RpathList) == 0 {
		printWarningRpath("未找到LC_RPATH配置")
	} else {
		for i, rpath := range info.RpathList {
			fmt.Printf("   %d. %s\n", i+1, rpath)
		}
	}

	// 打印自定义动态库
	fmt.Println("\n3. 自定义动态库:")
	if len(info.CustomLibraries) == 0 {
		printInfo("未找到自定义动态库")
	} else {
		for i, lib := range info.CustomLibraries {
			fmt.Printf("   %d. %s\n", i+1, lib)
		}
	}

	// 打印缺失的rpath
	fmt.Println("\n4. 缺失的RPATH配置:")
	if len(info.MissingRpaths) == 0 {
		printSuccess("所有自定义动态库的rpath配置正确")
	} else {
		for i, rpath := range info.MissingRpaths {
			printError(fmt.Sprintf("%d. %s", i+1, rpath))
		}
	}

	// 打印未正确加载的动态库
	fmt.Println("\n5. 未正确加载的动态库:")
	if len(info.UnloadedLibraries) == 0 {
		printSuccess("所有Frameworks中的动态库都已被主进程正确加载")
	} else {
		printWarningRpath(fmt.Sprintf("发现 %d 个未被主进程加载的动态库:", len(info.UnloadedLibraries)))
		for i, lib := range info.UnloadedLibraries {
			printError(fmt.Sprintf("%d. %s", i+1, lib))
		}
		printWarningRpath("这些库可能导致运行时加载失败，建议检查代码中的动态库引用")
	}

	fmt.Println(strings.Repeat("=", 60))
}

// fixRpathConfiguration 修复rpath配置
func fixRpathConfiguration(info *RpathInfo) error {
	if len(info.MissingRpaths) == 0 {
		return nil
	}

	printInfo(fmt.Sprintf("需要添加 %d 个rpath配置", len(info.MissingRpaths)))

	for _, rpath := range info.MissingRpaths {
		printInfo(fmt.Sprintf("添加rpath: %s", rpath))

		// 使用install_name_tool添加rpath
		cmd := exec.Command("install_name_tool", "-add_rpath", rpath, info.MainExecutable)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("添加rpath失败 %s: %w", rpath, err)
		}

		printSuccess(fmt.Sprintf("成功添加rpath: %s", rpath))
	}

	return nil
}

// fixUnloadedLibraries 修复未加载的动态库，将它们添加到主进程的Load command中
func fixUnloadedLibraries(info *RpathInfo) error {
	if len(info.UnloadedLibraries) == 0 {
		return nil
	}

	printInfo(fmt.Sprintf("需要修复 %d 个未加载的动态库", len(info.UnloadedLibraries)))

	for _, unloadedLib := range info.UnloadedLibraries {
		// 解析库信息，格式: "OpenDebugLog (framework) -> @rpath/OpenDebugLog/OpenDebugLog"
		parts := strings.Split(unloadedLib, " -> ")
		if len(parts) != 2 {
			printWarningRpath(fmt.Sprintf("跳过格式不正确的库信息: %s", unloadedLib))
			continue
		}

		libInfo := parts[0]
		expectedRpathRef := parts[1]

		// 提取库名和类型
		libNameParts := strings.Split(libInfo, " (")
		if len(libNameParts) != 2 {
			printWarningRpath(fmt.Sprintf("跳过格式不正确的库信息: %s", libInfo))
			continue
		}

		libName := libNameParts[0]
		libType := strings.TrimSuffix(libNameParts[1], ")")

		printInfo(fmt.Sprintf("修复未加载的%s: %s", libType, libName))

		// 构建实际的库文件路径
		var actualLibPath string
		if libType == "framework" {
			// 对于framework，路径格式为: Frameworks/LibName.framework/LibName
			actualLibPath = filepath.Join(info.AppPath, "Frameworks", libName+".framework", libName)
		} else if libType == "dylib" {
			// 对于dylib，路径格式为: Frameworks/libname.dylib
			actualLibPath = filepath.Join(info.AppPath, "Frameworks", libName)
		} else {
			printWarningRpath(fmt.Sprintf("不支持的库类型: %s", libType))
			continue
		}

		// 检查库文件是否存在
		if !utility.IsExist(actualLibPath) {
			printWarningRpath(fmt.Sprintf("库文件不存在，跳过: %s", actualLibPath))
			continue
		}

		// 使用install_name_tool添加库到主进程的Load command中
		printInfo(fmt.Sprintf("将 %s 添加到主进程Load command中...", expectedRpathRef))

		// 使用-change参数可能不适用，我们需要使用-add_load_command或者其他方法
		// 但install_name_tool没有直接添加load command的功能
		// 我们需要使用其他方法，比如修改二进制文件或者重新链接

		// 作为替代方案，我们可以检查是否可以通过修改库的install_name来解决
		// 首先获取库的当前install_name
		currentInstallName, err := getLibraryInstallName(actualLibPath)
		if err != nil {
			printWarningRpath(fmt.Sprintf("获取库install_name失败: %v", err))
			continue
		}

		printInfo(fmt.Sprintf("当前库install_name: %s", currentInstallName))
		printInfo(fmt.Sprintf("期望的引用路径: %s", expectedRpathRef))

		// 如果当前install_name与期望的引用路径不匹配，尝试修改
		if currentInstallName != expectedRpathRef {
			printInfo(fmt.Sprintf("修改库的install_name从 %s 到 %s", currentInstallName, expectedRpathRef))

			cmd := exec.Command("install_name_tool", "-id", expectedRpathRef, actualLibPath)
			if err := cmd.Run(); err != nil {
				printWarningRpath(fmt.Sprintf("修改库install_name失败: %v", err))
				continue
			}

			printSuccess(fmt.Sprintf("成功修改库install_name: %s", libName))
		}

		// 注意：仅修改install_name可能不足以解决问题
		// 如果主进程确实没有加载这个库，可能需要在代码层面添加对该库的引用
		// 或者使用其他工具来修改主进程的Load Commands
		printWarningRpath(fmt.Sprintf("已修改库 %s 的install_name，但如果主进程代码中没有引用该库，仍可能无法加载", libName))
		printWarningRpath("建议检查代码中是否正确引用了该库，或考虑使用dlopen等动态加载方式")
	}

	return nil
}

// getLibraryInstallName 获取库的install_name
func getLibraryInstallName(libPath string) (string, error) {
	cmd := exec.Command("otool", "-D", libPath)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行otool -D命令失败: %w", err)
	}

	lines := strings.Split(string(output), "\n")
	// 第一行是文件路径，第二行是install_name
	if len(lines) >= 2 {
		installName := strings.TrimSpace(lines[1])
		if installName != "" {
			return installName, nil
		}
	}

	return "", fmt.Errorf("未找到install_name")
}
